"""Dialog for plot appearance settings."""

from __future__ import annotations

from typing import Any, Dict, Optional

import tkinter as tk
from tkinter import ttk, messagebox

import config


class PlotSettingsDialog(tk.Toplevel):
    """Collect basic plot settings from the user."""

    def __init__(self, parent: tk.Misc, z_col: Optional[str] = None) -> None:
        super().__init__(parent)
        self.z_col = z_col
        self.result: Optional[Dict[str, Any]] = None
        self.title("Plot Settings")
        self.geometry("400x350")
        self._build_ui()

    def _build_ui(self) -> None:
        self.point_size_var = tk.StringVar(value="50")
        self.bin_size_var = tk.StringVar(value="30")
        self.x_title_var = tk.StringVar(value="")
        self.y_title_var = tk.StringVar(value="")
        ttk.Label(self, text="Point Size:").pack(anchor="w", padx=5, pady=2)
        ttk.Entry(self, textvariable=self.point_size_var).pack(fill=tk.X, padx=5)
        ttk.Label(self, text="Histogram Bins:").pack(anchor="w", padx=5, pady=2)
        ttk.Entry(self, textvariable=self.bin_size_var).pack(fill=tk.X, padx=5)
        ttk.Label(self, text="X Title:").pack(anchor="w", padx=5, pady=2)
        ttk.Entry(self, textvariable=self.x_title_var).pack(fill=tk.X, padx=5)
        ttk.Label(self, text="Y Title:").pack(anchor="w", padx=5, pady=2)
        ttk.Entry(self, textvariable=self.y_title_var).pack(fill=tk.X, padx=5)
        if self.z_col:
            self.colormap_var = tk.StringVar(value="viridis")
            ttk.Label(self, text="Colormap:").pack(anchor="w", padx=5, pady=2)
            ttk.Combobox(
                self,
                textvariable=self.colormap_var,
                values=config.get_colormap_options("Sequential"),
            ).pack(fill=tk.X, padx=5)
            self.show_cb_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(self, text="Show Colorbar", variable=self.show_cb_var).pack(anchor="w", padx=5, pady=2)
        ttk.Button(self, text="Submit", command=self._on_submit).pack(side=tk.LEFT, padx=5, pady=10)
        ttk.Button(self, text="Cancel", command=self._on_cancel).pack(side=tk.RIGHT, padx=5, pady=10)

    def _on_submit(self) -> None:
        try:
            point_size = float(self.point_size_var.get())
            bins = int(self.bin_size_var.get())
        except ValueError:
            messagebox.showerror("Error", "Invalid numeric value")
            return
        self.result = {
            "point_size": point_size,
            "bin_size": bins,
            "x_title_text": self.x_title_var.get(),
            "y_title_text": self.y_title_var.get(),
            "downsampling_factor": 1,
        }
        if self.z_col:
            cmap = self.colormap_var.get()
            if not config.validate_colormap(cmap):
                cmap = "viridis"
            self.result.update({
                "colormap": cmap,
                "show_colorbar": self.show_cb_var.get(),
            })
        self.destroy()

    def _on_cancel(self) -> None:
        self.result = None
        self.destroy()

    def show(self) -> Optional[Dict[str, Any]]:
        self.wait_window()
        return self.result
