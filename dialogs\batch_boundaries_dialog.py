"""Dialog for selecting boundaries for all wells from an Excel dataframe."""

from __future__ import annotations

from typing import Dict, List, Tuple

import tkinter as tk
from tkinter import ttk
import pandas as pd


class BatchBoundariesDialog(tk.Toplevel):
    """Dialog to choose boundaries for each well."""

    def __init__(self, parent: tk.Misc, df: pd.DataFrame, las_well_names: List[str]) -> None:
        super().__init__(parent)
        self.title("Select Boundaries for All Wells")
        self.geometry("700x500")
        self.df = df
        self.las_well_names = las_well_names
        self.result: Dict[str, Tuple[float, float]] = {}
        self._build_ui()

    def _build_ui(self) -> None:
        available = [w for w in self.las_well_names if w in self.df["Well"].unique()]
        frame = ttk.Frame(self, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable = ttk.Frame(canvas)
        scrollable.bind("<Configure>", lambda _e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill=tk.BOTH, expand=True)
        scrollbar.pack(side="right", fill=tk.Y)

        self.entries: Dict[str, Tuple[tk.Entry, tk.Entry]] = {}
        for i, well in enumerate(available):
            ttk.Label(scrollable, text=well).grid(row=i, column=0, padx=5, pady=5)
            top_e = ttk.Entry(scrollable)
            bottom_e = ttk.Entry(scrollable)
            top_e.grid(row=i, column=1, padx=5, pady=5)
            bottom_e.grid(row=i, column=2, padx=5, pady=5)
            self.entries[well] = (top_e, bottom_e)
        ttk.Button(self, text="OK", command=self._on_ok).pack(pady=5)

    def _on_ok(self) -> None:
        for well, (top_e, bottom_e) in self.entries.items():
            try:
                top = float(top_e.get())
                bottom = float(bottom_e.get())
            except ValueError:
                continue
            self.result[well] = (top, bottom)
        self.destroy()

    def get_boundaries(self) -> Dict[str, Tuple[float, float]]:
        """Return selected boundaries."""
        return self.result


def select_boundaries_for_all_wells(df: pd.DataFrame, las_well_names: List[str]):
    root = tk.Tk()
    root.withdraw()
    dlg = BatchBoundariesDialog(root, df, las_well_names)
    root.wait_window(dlg)
    root.destroy()
    return dlg.get_boundaries()
