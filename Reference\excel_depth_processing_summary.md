# Excel File Selection and Depth Range Determination Workflow

This document outlines the Python files and functions responsible for handling Excel file selection and determining depth ranges for analysis within the provided codebase. This can serve as a guide if you intend to implement similar functionalities in other projects.

## Core Modules and Responsibilities

The primary logic for these operations is encapsulated within the `ui` directory, specifically in `file_management.py` and `dialog_systems.py`. The main script, `load_multilas_EEI_XCOR_PLOT_Final.py`, delegates these tasks to these specialized modules.

### 1. Excel File Selection

The process of selecting an Excel file containing boundary or depth information is primarily managed by:

*   **File:** [`ui/file_management.py`](ui/file_management.py)
    *   **Function:** [`load_boundaries_from_excel(title="Select Excel file with boundary information")`](ui/file_management.py:264)
        *   **Purpose:** This is the core function for user interaction regarding Excel file selection.
        *   **Action:** It uses `tkinter.filedialog.askopenfilename` to display a standard OS file selection dialog, allowing the user to browse and choose an `.xls` or `.xlsx` file.
        *   **Output:** Reads the selected Excel file into a pandas DataFrame.
        *   **Validation:** Performs initial checks to ensure the DataFrame is not empty and contains essential columns like 'Well', 'Surface', and 'MD' (Measured Depth).
    *   **Function:** [`load_excel_depth_ranges(las_files)`](ui/file_management.py:354)
        *   **Purpose:** Acts as a higher-level orchestrator for loading depth ranges specifically.
        *   **Action:**
            1.  Prompts the user (via `messagebox.askyesno`) if they wish to load an Excel file for depth ranges.
            2.  If the user agrees, it calls [`load_boundaries_from_excel()`](ui/file_management.py:264) to handle the actual file selection and reading.
            3.  It then calls [`filter_excel_data_for_las_wells()`](ui/file_management.py:319) to ensure that the data from the Excel file is relevant to the LAS files currently loaded in the application.

*   **File:** [`ui/dialog_systems.py`](ui/dialog_systems.py)
    *   **Function:** [`get_depth_ranges(self, las_files, log_keywords_for_finding_cols, preloaded_excel_df=None)`](ui/dialog_systems.py:984)
        *   **Purpose:** While its main goal is to obtain depth ranges (see next section), this function also includes logic for initiating the Excel file loading process if the user chooses the "Excel import" method and if an Excel DataFrame (`preloaded_excel_df`) hasn't already been provided.
        *   **Action:** If Excel import is chosen and no data is preloaded, it will trigger the file selection workflow, which relies on the functions in [`ui/file_management.py`](ui/file_management.py).

**Summary for Excel File Selection:**
The user is prompted to select an Excel file through a dialog managed by [`load_boundaries_from_excel()`](ui/file_management.py:264). Higher-level functions like [`load_excel_depth_ranges()`](ui/file_management.py:354) and [`get_depth_ranges()`](ui/dialog_systems.py:984) coordinate this step within the broader application workflow.

### 2. Depth Range Determination

Once an Excel file is loaded (or if manual input is chosen), the determination of specific top and bottom depth values for analysis is handled by:

*   **File:** [`ui/dialog_systems.py`](ui/dialog_systems.py)
    *   **Function:** [`get_depth_ranges(self, las_files, log_keywords_for_finding_cols, preloaded_excel_df=None)`](ui/dialog_systems.py:984)
        *   **Purpose:** This is the central function for acquiring the depth ranges for each well.
        *   **Action:**
            *   It presents a UI to the user, allowing them to choose the method for defining boundaries:
                *   **Manual Input:** If selected, it calls an internal helper `create_manual_ui()` (around line [`ui/dialog_systems.py:1109`](ui/dialog_systems.py:1109)) which generates `tkinter.Entry` fields for the user to type in top and bottom depth values for each well. Default values are often pre-filled based on the min/max depths of available logs (e.g., DT log).
                *   **Import from Excel File:** If selected (and an Excel DataFrame is available, either preloaded or loaded via the functions mentioned in the previous section):
                    *   It calls [`self._select_boundaries_for_all_wells()`](ui/dialog_systems.py:614) to present a dialog where the user can pick top and bottom surfaces for all wells, potentially using common surfaces.
                    *   Alternatively, for more granular control (though the primary path for "all wells" is `_select_boundaries_for_all_wells`), the logic could involve [`self.select_boundaries_from_excel()`](ui/dialog_systems.py:430) for individual well boundary selection from the Excel data.
    *   **Function:** [`select_boundaries_from_excel(self, df, well_name)`](ui/dialog_systems.py:430)
        *   **Purpose:** Provides a UI dialog for selecting top and bottom boundaries for a *single specified well*.
        *   **Action:** It filters the provided DataFrame (`df`) for the given `well_name` and populates comboboxes with the available 'Surface' names from the Excel data. The user selects a top surface and a bottom surface, and their corresponding 'MD' values are retrieved.
    *   **Function:** [`_select_boundaries_for_all_wells(self, df, las_well_names)`](ui/dialog_systems.py:614)
        *   **Purpose:** Offers a more comprehensive UI for setting boundaries for *all loaded LAS wells* at once, using the data from the Excel DataFrame (`df`).
        *   **Action:**
            *   It identifies common surfaces across the wells present in the Excel data.
            *   Presents a dialog where the user can:
                *   Choose to apply common top and bottom surfaces to all wells.
                *   Individually select top and bottom surfaces for each well from dropdowns populated with surfaces relevant to that well (as per the Excel data).
            *   The selected surfaces' 'MD' values are then used as the top and bottom depths.

**Summary for Depth Range Determination:**
The [`get_depth_ranges()`](ui/dialog_systems.py:984) function is key. It allows users to either type in depths manually or select geological surfaces from a previously loaded Excel file. The selection from Excel is facilitated by [`_select_boundaries_for_all_wells()`](ui/dialog_systems.py:614) for batch processing or [`select_boundaries_from_excel()`](ui/dialog_systems.py:430) for individual wells, which then translate to specific depth values.

## Main Script Orchestration

*   **File:** [`load_multilas_EEI_XCOR_PLOT_Final.py`](load_multilas_EEI_XCOR_PLOT_Final.py)
    *   This script acts as the entry point and overall orchestrator.
    *   It calls functions from the `ui` module (e.g., `ui.dialog_systems.get_depth_ranges`, `ui.file_management.load_excel_depth_ranges`) to handle these specific tasks.
    *   For example, within its `run_eei_analysis` function (or a similar main workflow function, likely now in [`ui/workflow_orchestration.py`](ui/workflow_orchestration.py) based on the refactoring), it would:
        1.  Optionally call [`load_excel_depth_ranges()`](ui/file_management.py:354) to allow pre-loading of Excel data.
        2.  Call [`get_depth_ranges()`](ui/dialog_systems.py:984), passing any preloaded Excel data, to obtain the final depth ranges to be used in subsequent analysis steps.

By modularizing these functionalities, the codebase becomes easier to manage, test, and adapt. If you need to replicate this in another project, consider creating similar `file_management` and `dialog_systems` (or equivalent UI handling) modules.