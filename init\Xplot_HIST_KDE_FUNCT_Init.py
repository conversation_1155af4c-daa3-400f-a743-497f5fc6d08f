# -*- coding: utf-8 -*-
"""
Created on Thu Aug 29 11:15:20 2024

@author: devri.agustianto
"""


import numpy as np
import lasio
import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import matplotlib.pyplot as plt

from xplot_app import config
from xplot_app import data_io
from xplot_app import processing
from xplot_app.plotting import create_plot, show_post_plot_options
from scipy import stats
from scipy import interpolate
import pandas as pd
import re
from xplot_app.statistical_analyzers import (
    HistogramAnalyzer,
    KDEAnalyzer,
    StatisticalAnalyzer,
    StatisticalVisualizer,
)


# Define keywords for broader log detection (similar to reference file)
from xplot_app.dialogs.depth_dialog import get_depth_ranges
from xplot_app.dialogs.batch_boundaries_dialog import select_boundaries_for_all_wells
from xplot_app.dialogs.calculator_dialog import CalculatorDialog
from xplot_app.dialogs.column_select_dialog import ColumnSelectionDialog
from xplot_app.dialogs.plot_settings_dialog import PlotSettingsDialog
# This helps in identifying common log types with various mnemonics
log_keywords = config.log_keywords
COLORMAP_CATEGORIES = config.COLORMAP_CATEGORIES
get_colormap_options = config.get_colormap_options
get_sequential_subcategories = config.get_sequential_subcategories
validate_colormap = config.validate_colormap
apply_colormap_reversal = config.apply_colormap_reversal

find_default_columns = processing.find_default_columns
analyze_log_availability = processing.analyze_log_availability
validate_calculation_inputs = processing.validate_calculation_inputs
interpolate_nan = processing.interpolate_nan
interpolate_class = processing.interpolate_class
validate_data_for_plotting = processing.validate_data_for_plotting
get_robust_limits = processing.get_robust_limits
execute_custom_calculations = processing.execute_custom_calculations


def get_calculations(las_files):
    """Show the calculator dialog and apply calculations to LAS files."""

    root = tk.Tk()
    root.withdraw()
    dlg = CalculatorDialog(root)
    calc_text = dlg.show()
    root.destroy()

    if calc_text is None:
        return False

    if calc_text:
        for las in las_files:
            execute_custom_calculations(las, calc_text)
    return True

def get_column_names_and_depths(las_files, preloaded_excel_df=None):
    """Show the column selection dialog."""

    root = tk.Tk()
    root.withdraw()
    dlg = ColumnSelectionDialog(root, las_files, preloaded_excel_df)
    result = dlg.show()
    root.destroy()
    return result


def get_plot_settings(las_files, x_col, y_col, class_col, depth_ranges, z_col=None):
    """Show the plot settings dialog."""
    root = tk.Tk()
    root.withdraw()
    dlg = PlotSettingsDialog(root, z_col)
    result = dlg.show()
    root.destroy()
    return result


def main():
    print("\n=== DEBUG: MAIN FUNCTION EXECUTION ===")
    print("Starting application...")

    while True:  # Main loop for restart functionality
        try:
            # Clear any existing matplotlib figures
            plt.close('all')

            las_files = data_io.load_multiple_las_files()
            if not las_files:
                print("No files selected. Exiting.")
                return

            print(f"Successfully loaded {len(las_files)} LAS files")
            for i, las in enumerate(las_files):
                well_name = las.well.WELL.value
                print(f"  LAS file {i+1}: {well_name}")
                print(f"    Depth range: {min(las['DEPTH'].data)} to {max(las['DEPTH'].data)}")
                print(f"    Number of curves: {len(las.curves)}")

            # Get calculations from user
            print("\nPrompting user for calculations...")
            success = get_calculations(las_files)
            if not success:
                print("Calculations aborted. Exiting.")
                return
            print("Calculations completed successfully")

            # Prompt user to load Excel file with depth ranges upfront
            # Pass the LAS files to filter the Excel data to only include matching wells
            # Assuming log_keywords is defined globally or accessible
            # TODO: Ensure log_keywords is properly defined and passed if needed by load_excel_depth_ranges
            preloaded_excel_df = data_io.load_excel_depth_ranges(las_files)
            if preloaded_excel_df is not None:
                print(f"Excel file with depth ranges loaded successfully.")
                print(f"Found {len(preloaded_excel_df)} boundary entries for {preloaded_excel_df['Well'].nunique()} wells.")
                print("This data will be automatically used in the depth ranges dialog.")


            print("\nPrompting user for column selection and depth ranges...")
            # Pass preloaded_excel_df to get_column_names_and_depths
            result = get_column_names_and_depths(las_files, preloaded_excel_df=preloaded_excel_df)
            if result is None:
                # Error messages are handled within get_column_names_and_depths or get_depth_ranges
                print("Column and depth selection was cancelled or failed. Exiting.")
                return
            print("Column selection and depth ranges completed")

            x_col = result["x_col"]
            y_col = result["y_col"]
            class_col = result["class_col"]
            z_col = result["z_col"]
            depth_ranges = result["depth_ranges"]

            print(f"\nSelected columns: x_col={x_col}, y_col={y_col}, class_col={class_col}, z_col={z_col}")
            print(f"Selected depth ranges: {depth_ranges}")

            # Check if validation_stats is available from the enhanced get_column_names_and_depths function
            validation_stats = result.get("validation_stats")
            if validation_stats:
                print("Validation statistics available from column selection")
            else:
                print("No validation statistics available from column selection")

            # Check required information (class_col is optional)
            if not all([x_col, y_col, depth_ranges]):
                print("Missing required information. Exiting.")
                return

            # Perform comprehensive validation if not already done
            if validation_stats is None:
                validation_result = validate_data_for_plotting(las_files, x_col, y_col, class_col, depth_ranges, z_col)

                # If data is invalid, show error message and ask if user wants to proceed
                if not validation_result['valid']:
                    error_message = "The following issues were detected with the selected data:\n\n"
                    for issue in validation_result['issues']:
                        error_message += f"• {issue}\n"

                    error_message += "\nThe plot may not display correctly. Do you want to proceed anyway?"
                    proceed = messagebox.askyesno("Data Validation Warning", error_message)

                    if not proceed:
                        print("Plot creation aborted due to validation issues.")
                        return

                # If there are warnings but data is valid, show them to the user
                elif validation_result['warnings']:
                    warning_message = "The following issues were detected with the selected data:\n\n"
                    for warning in validation_result['warnings']:
                        warning_message += f"• {warning}\n"

                    warning_message += "\nThe plot may not display optimally. Do you want to proceed?"
                    proceed = messagebox.askyesno("Data Validation Warning", warning_message)

                    if not proceed:
                        print("Plot creation aborted due to validation warnings.")
                        return

                validation_stats = validation_result['stats']

            settings = get_plot_settings(las_files, x_col, y_col, class_col, depth_ranges, z_col)
            if settings is None:
                print("Plot settings not provided. Exiting.")
                return

            # Print some information about the data
            print("\nSelected columns:")
            print(f"X-axis: {x_col}")
            print(f"Y-axis: {y_col}")
            print(f"Class: {class_col}")
            print("\nDepth ranges:")
            for well, (top, bottom) in depth_ranges.items():
                print(f"{well}: {top} - {bottom}")

            # Print validation statistics
            print("\nData statistics:")
            print(f"Total data points: {validation_stats['total_points']}")
            print(f"Valid data points: {validation_stats['valid_points']} " +
                  f"({validation_stats['valid_points']/max(1, validation_stats['total_points'])*100:.1f}%)")
            print(f"X-axis unique values: {validation_stats['x_unique_values']}")
            print(f"Y-axis unique values: {validation_stats['y_unique_values']}")
            if validation_stats['class_unique_values'] is not None:
                print(f"Class unique values: {validation_stats['class_unique_values']}")
            if validation_stats.get('z_unique_values') is not None:
                print(f"Z-axis unique values: {validation_stats['z_unique_values']}")

            print("\nWells with valid data:")
            for well in validation_stats['wells_with_data']:
                print(f"• {well}")

            if validation_stats['wells_without_data']:
                print("\nWells without valid data:")
                for well in validation_stats['wells_without_data']:
                    print(f"• {well}")

            # Create the plot and capture the user's next choice
            choice = create_plot(
                las_files,
                x_col,
                y_col,
                class_col,
                depth_ranges,
                settings,
                z_col,
            )

            if choice == 'exit':
                print("User chose to exit. Closing application.")
                break
            elif choice == 'restart':
                print("User chose to restart. Starting new analysis...")
                continue
            else:
                # Default to exit if unexpected choice
                print("Unexpected choice. Exiting application.")
                break

        except Exception as e:
            print(f"An error occurred during analysis: {str(e)}")
            # Show options even if there's an error
            try:
                choice = show_post_plot_options()
                if choice == 'exit':
                    print("User chose to exit after error. Closing application.")
                    break
                elif choice == 'restart':
                    print("User chose to restart after error. Starting new analysis...")
                    continue
                else:
                    print("Exiting due to error.")
                    break
            except Exception as dialog_error:
                print(f"Error showing post-plot dialog: {str(dialog_error)}")
                break

if __name__ == "__main__":
    main()
