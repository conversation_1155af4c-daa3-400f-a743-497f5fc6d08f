"""Input/output utilities for loading LAS and Excel files."""

from __future__ import annotations

from typing import List, Optional, Dict
import os
import numpy as np

import lasio
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
import logging

logger = logging.getLogger(__name__)


def find_default_columns(las, keywords):
    """
    Find the default columns for specific logs based on the provided keywords.
    This version includes detailed logging for troubleshooting.
    """
    default_columns = {}
    for keyword, aliases in keywords.items():
        found = False
        for alias in aliases:
            for curve in las.curves:
                # Case-insensitive comparison
                if alias.upper() == curve.mnemonic.upper():
                    default_columns[keyword] = curve.mnemonic
                    found = True
                    break
            if found:
                break
        if not found:
            default_columns[keyword] = None
    return default_columns


def load_multiple_las_files() -> Optional[List[lasio.LASFile]]:
    """Prompt the user to select and load multiple LAS files."""
    root = tk.Tk()
    root.withdraw()

    file_paths = filedialog.askopenfilenames(
        title="Select LAS files",
        filetypes=[("LAS files", "*.las")],
    )

    if not file_paths:
        logger.info("No files selected by user.")
        return None

    las_files: List[lasio.LASFile] = []
    for path in file_paths:
        try:
            las = lasio.read(path)
            # Store the file path for reference
            las.file_path = path
            las_files.append(las)
            logger.info(f"Loaded file: {path}")
        except Exception as exc:  # noqa: BLE001
            logger.error(f"Error loading file {path}: {str(exc)}")
            messagebox.showerror("LAS Load Error", f"Error loading file {path}:\n{str(exc)}")

    if not las_files:
        logger.error("No LAS files were successfully loaded.")
        return None

    logger.info(f"Successfully loaded {len(las_files)} LAS files")
    return las_files


def load_boundaries_from_excel(title: str = "Select Excel file with boundary information") -> Optional[pd.DataFrame]:
    """
    Load boundary information from an Excel file.

    Args:
        title: The title to display in the file dialog

    Returns:
        DataFrame containing well names, surface names, and measured depths
        or None if no file was selected or an error occurred.
    """
    root = tk.Tk()
    root.withdraw()

    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")]
    )

    if not file_path:
        logger.info("No Excel file selected for boundaries.")
        return None

    try:
        # Load the Excel file
        df = pd.read_excel(file_path)

        # Check if the required columns exist
        required_columns = ['Well', 'Surface', 'MD']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            err_msg = (f"The Excel file is missing the following required columns: {', '.join(missing_columns)}.\n"
                       f"Please ensure the file contains columns named: {', '.join(required_columns)}")
            messagebox.showerror("Missing Columns", err_msg)
            logger.error(err_msg)
            return None

        # Basic validation
        if df.empty:
            err_msg = "The Excel file contains no data."
            messagebox.showerror("Empty File", err_msg)
            logger.error(err_msg)
            return None

        logger.info(f"Successfully loaded boundary data from {file_path}")
        logger.info(f"Found {len(df)} boundary entries for {df['Well'].nunique()} wells")
        return df

    except Exception as e:
        err_msg = f"An error occurred while loading the Excel file:\n{str(e)}"
        messagebox.showerror("Error Loading File", err_msg)
        logger.error(f"Error loading Excel file: {str(e)}")
        return None


def filter_excel_data_for_las_wells(df: pd.DataFrame, las_files: List[lasio.LASFile]) -> Optional[pd.DataFrame]:
    """
    Filter Excel data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        df: DataFrame containing well names, surface names, and measured depths
        las_files: List of LAS file objects

    Returns:
        Filtered DataFrame containing only boundaries for wells in the LAS files
    """
    if df is None:
        return None

    # Get the list of well names from the LAS files
    las_well_names = [las.well.WELL.value for las in las_files]

    # Filter the DataFrame to only include rows where the Well column value is in las_well_names
    filtered_df = df[df['Well'].isin(las_well_names)]

    # Check if we have any matching wells
    if filtered_df.empty:
        logger.warning("No matching wells found in Excel file. Excel wells: "
                       f"{', '.join(df['Well'].unique())}. LAS wells: {', '.join(las_well_names)}")
        messagebox.showwarning("No Matching Wells", "No wells in the Excel boundary file match the loaded LAS files.")
        return None

    # Log the filtering results
    original_well_count = df['Well'].nunique()
    filtered_well_count = filtered_df['Well'].nunique()
    logger.info(f"Filtered Excel data from {original_well_count} wells to {filtered_well_count} wells that match loaded LAS files")
    logger.info(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")

    return filtered_df


def load_excel_depth_ranges(las_files: List[lasio.LASFile]) -> Optional[pd.DataFrame]:
    """
    Prompt the user to load an Excel file with depth ranges at the beginning of the program.
    Filter the data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        las_files: List of LAS file objects

    Returns:
        DataFrame containing well names, surface names, and measured depths (filtered for LAS wells)
        or None if no file was selected, the user canceled, or an error occurred.
    """
    # Ask user if they want to load an Excel file with depth ranges
    root = tk.Tk()
    root.withdraw()

    load_excel = messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\n"
        "The file should have columns named 'Well', 'Surface', and 'MD'."
    )

    if not load_excel:
        logger.info("User chose not to load Excel file with depth ranges at this time.")
        return None

    # Load the Excel file
    df = load_boundaries_from_excel("Select Excel file with depth ranges")

    # Filter the data to only include boundaries for wells in the LAS files
    return filter_excel_data_for_las_wells(df, las_files)
