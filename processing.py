"""Core data processing and validation utilities."""

from __future__ import annotations

from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import lasio
from scipy import interpolate
import re


def find_default_columns(las: lasio.LASFile, keywords: Dict[str, List[str]]) -> Dict[str, Optional[str]]:
    """Return a mapping of log keywords to actual curve names in *las* if present."""
    default_columns: Dict[str, Optional[str]] = {}
    for keyword, aliases in keywords.items():
        found = False
        for alias in aliases:
            for curve in las.curves:
                if alias.upper() == curve.mnemonic.upper():
                    default_columns[keyword] = curve.mnemonic
                    found = True
                    break
            if found:
                break
        if not found:
            default_columns[keyword] = None
    return default_columns


def analyze_log_availability(las_files: List[lasio.LASFile]) -> Dict[str, Any]:
    """Analyze which logs are available across *las_files*."""
    all_logs: Dict[str, Dict[str, Any]] = {}
    well_count = len(las_files)
    for las in las_files:
        well_name = las.well.WELL.value
        for curve_name in las.curves.keys():
            if curve_name not in all_logs:
                all_logs[curve_name] = {"wells": [], "count": 0}
            all_logs[curve_name]["wells"].append(well_name)
            all_logs[curve_name]["count"] += 1

    common_logs: List[str] = []
    partial_logs: Dict[str, Any] = {}
    for log_name, log_info in all_logs.items():
        if log_info["count"] == well_count:
            common_logs.append(log_name)
        else:
            partial_logs[log_name] = log_info

    return {
        "common_logs": sorted(common_logs),
        "partial_logs": partial_logs,
        "total_wells": well_count,
    }


def validate_calculation_inputs(las_files: List[lasio.LASFile], calculation_text: str) -> Dict[str, Any]:
    """Validate that all logs referenced in *calculation_text* exist."""
    lines = [line.strip() for line in calculation_text.split("\n") if line.strip()]
    output_variables: set[str] = set()
    input_variables: set[str] = set()
    for line in lines:
        if line.startswith("#"):
            continue
        if "=" in line and not any(op in line for op in ["==", "!=", "<=", ">="]):
            if any(op in line for op in ["+=", "-=", "*=", "/="]):
                left_side = line.split("=")[0].strip()
                right_side = "=".join(line.split("=")[1:]).strip()
            else:
                parts = line.split("=", 1)
                if len(parts) != 2:
                    continue
                left_side, right_side = parts[0].strip(), parts[1].strip()
            output_var = re.match(r"^([A-Z_][A-Z0-9_]*)", left_side.upper())
            if output_var:
                output_variables.add(output_var.group(1))
            right_vars = set(re.findall(r"\b([A-Z_][A-Z0-9_]*)\b", right_side.upper()))
            input_variables.update(right_vars)
        else:
            line_vars = set(re.findall(r"\b([A-Z_][A-Z0-9_]*)\b", line.upper()))
            input_variables.update(line_vars)

    numpy_functions = {
        "NP",
        "NUMPY",
        "LOG",
        "SQRT",
        "EXP",
        "SIN",
        "COS",
        "TAN",
        "NANMIN",
        "NANMAX",
        "NANMEAN",
        "NANSTD",
        "WHERE",
        "ISNAN",
        "ISFINITE",
        "CLIP",
        "ABS",
        "ABSOLUTE",
        "MIN",
        "MAX",
        "MEAN",
        "STD",
        "SUM",
        "PROD",
        "ROUND",
        "FLOOR",
        "CEIL",
        "POWER",
        "POW",
        "SIGN",
        "ARRAY",
        "ZEROS",
        "ONES",
        "CONCATENATE",
        "STACK",
        "RESHAPE",
        "FLATTEN",
        "ARCSIN",
        "ARCCOS",
        "ARCTAN",
        "ARCTAN2",
        "SINH",
        "COSH",
        "TANH",
        "MEDIAN",
        "PERCENTILE",
        "QUANTILE",
        "VAR",
        "CORRCOEF",
        "COV",
        "ALL",
        "ANY",
        "LOGICAL_AND",
        "LOGICAL_OR",
        "LOGICAL_NOT",
        "GREATER",
        "LESS",
        "EQUAL",
        "NOT_EQUAL",
        "GREATER_EQUAL",
        "LESS_EQUAL",
    }
    python_keywords = {
        "IF",
        "ELSE",
        "FOR",
        "WHILE",
        "DEF",
        "CLASS",
        "IMPORT",
        "FROM",
        "AS",
        "RETURN",
        "TRUE",
        "FALSE",
        "NONE",
    }

    input_variables = input_variables - output_variables - numpy_functions - python_keywords

    common_logs = set(las_files[0].curves.keys())
    for las in las_files[1:]:
        common_logs.intersection_update(las.curves.keys())

    missing_logs: Dict[str, List[str]] = {}
    all_missing: set[str] = set()
    for las in las_files:
        well_name = las.well.WELL.value
        well_curves = set(las.curves.keys())
        missing_in_well = input_variables - well_curves
        if missing_in_well:
            missing_logs[well_name] = list(missing_in_well)
            all_missing.update(missing_in_well)

    is_valid = len(all_missing) == 0
    error_details = ""
    if not is_valid:
        error_details = "CALCULATION ERROR: Missing Input Logs\n\n"
        error_details += "⚠️ WARNING: Your calculations reference INPUT logs that are NOT available in all wells.\n"
        error_details += "This will cause calculation failures in wells where these logs are missing.\n\n"

        if output_variables:
            error_details += f"📊 OUTPUT variables being created: {', '.join(sorted(output_variables))}\n"
            error_details += "✅ These are fine - they will be created by your calculations.\n\n"

        error_details += "❌ MISSING INPUT LOGS:\n\n"

        for las in las_files:
            well_name = las.well.WELL.value
            if well_name in missing_logs:
                missing = missing_logs[well_name]
                available_in_well = input_variables.intersection(set(las.curves.keys()))
                error_details += f"Well: {well_name}\n"
                if missing:
                    error_details += f"  ❌ Missing INPUT logs: {', '.join(missing)}\n"
                if available_in_well:
                    error_details += f"  ✅ Available INPUT logs: {', '.join(available_in_well)}\n"
                error_details += "\n"

        # Enhanced summary with availability analysis
        error_details += "AVAILABILITY ANALYSIS:\n"
        for log in all_missing:
            wells_missing = [well for well, missing in missing_logs.items() if log in missing]
            wells_with_log = len(las_files) - len(wells_missing)
            error_details += f"- {log}: Available in {wells_with_log}/{len(las_files)} wells "
            error_details += f"(Missing from: {', '.join(wells_missing)})\n"

        error_details += "\n💡 SOLUTION OPTIONS:\n"
        error_details += "1. Remove references to ❌ missing INPUT logs from your calculations\n"
        error_details += "2. Use only ✅ logs that are available in all wells\n"
        error_details += "3. Check the calculator legend for safe log options\n"
        error_details += "4. Use 'Check Log Availability' button before submitting\n\n"

        error_details += "🔍 SAFE INPUT LOGS TO USE:\n"
        safe_logs = input_variables.intersection(common_logs)
        if safe_logs:
            error_details += f"✅ Available in all wells: {', '.join(sorted(safe_logs))}\n"
        else:
            error_details += "⚠️ None of your referenced INPUT logs are available in all wells!\n"

        # Show all available logs for reference
        error_details += f"\n📋 ALL AVAILABLE LOGS (in all wells): {', '.join(sorted(common_logs))}\n"

        error_details += "\n📋 NEXT STEPS:\n"
        error_details += "[Retry] - Modify calculations to use only ✅ logs\n"
        error_details += "[Skip] - Continue workflow without custom calculations\n"
        error_details += "[Cancel] - Return to main workflow"

    return {
        "valid": is_valid,
        "missing_logs": missing_logs,
        "available_logs": list(common_logs),
        "error_details": error_details,
    }


def interpolate_nan(depth: np.ndarray, data: np.ndarray) -> np.ndarray:
    """Interpolate NaN values in *data* based on *depth*."""
    mask = ~np.isnan(data)
    if np.sum(mask) < 2:
        return data
    f = interpolate.interp1d(depth[mask], data[mask], kind="linear", fill_value="extrapolate")
    return f(depth)


def interpolate_class(depth: np.ndarray, class_data: np.ndarray) -> np.ndarray:
    """Interpolate class values using nearest-neighbour approach."""
    mask = ~np.isnan(class_data)
    if np.sum(mask) < 1:
        return class_data
    f = interpolate.interp1d(depth[mask], class_data[mask], kind="nearest", fill_value="extrapolate")
    return f(depth)


def validate_data_for_plotting(
    las_files: List[lasio.LASFile],
    x_col: str,
    y_col: str,
    class_col: Optional[str],
    depth_ranges: Dict[str, Tuple[float, float]],
    z_col: Optional[str] = None,
) -> Dict[str, Any]:
    """Validate data extracted from *las_files* for plotting."""
    result = {
        "valid": True,
        "issues": [],
        "warnings": [],
        "stats": {
            "total_points": 0,
            "valid_points": 0,
            "x_unique_values": 0,
            "y_unique_values": 0,
            "class_unique_values": None,
            "z_unique_values": None,
            "wells_with_data": [],
            "wells_without_data": [],
        },
    }

    MIN_VALID_POINTS = 1
    MIN_UNIQUE_VALUES = 1

    all_x_data: List[float] = []
    all_y_data: List[float] = []
    all_class_data: List[float] = []
    all_z_data: List[float] = []

    for las in las_files:
        well_name = las.well.WELL.value
        if well_name not in depth_ranges:
            result["warnings"].append(f"Depth range not specified for well {well_name}")
            result["stats"]["wells_without_data"].append(well_name)
            continue

        top_depth, bottom_depth = depth_ranges[well_name]
        missing_curves = []
        if "DEPTH" not in las.curves:
            missing_curves.append("DEPTH")
        if x_col not in las.curves:
            missing_curves.append(x_col)
        if y_col not in las.curves:
            missing_curves.append(y_col)
        if z_col and z_col not in las.curves:
            missing_curves.append(z_col)
        if missing_curves:
            result["warnings"].append(
                f"Well {well_name} missing required curves: {', '.join(missing_curves)}"
            )
            result["stats"]["wells_without_data"].append(well_name)
            continue

        depth = np.array(las["DEPTH"].data)
        mask = (depth >= top_depth) & (depth <= bottom_depth)
        x_data = np.array(las[x_col].data)[mask]
        y_data = np.array(las[y_col].data)[mask]
        if class_col:
            class_data = np.array(las[class_col].data)[mask]
        else:
            class_data = np.array([])
        if z_col:
            z_data = np.array(las[z_col].data)[mask]
        else:
            z_data = np.array([])

        valid_mask = np.isfinite(x_data) & np.isfinite(y_data)
        if class_col:
            valid_mask &= np.isfinite(class_data)
        if z_col:
            valid_mask &= np.isfinite(z_data)

        x_data = x_data[valid_mask]
        y_data = y_data[valid_mask]
        class_data = class_data[valid_mask] if class_col else class_data
        z_data = z_data[valid_mask] if z_col else z_data

        if len(x_data) < MIN_VALID_POINTS or len(y_data) < MIN_VALID_POINTS:
            result["warnings"].append(f"Not enough valid data in well {well_name}")
            result["stats"]["wells_without_data"].append(well_name)
            continue

        result["stats"]["wells_with_data"].append(well_name)

        all_x_data.extend(x_data)
        all_y_data.extend(y_data)
        if class_col:
            all_class_data.extend(class_data)
        if z_col:
            all_z_data.extend(z_data)

    all_x_np = np.array(all_x_data)
    all_y_np = np.array(all_y_data)
    if class_col:
        all_class_np = np.array(all_class_data)
    else:
        all_class_np = np.array([])
    if z_col:
        all_z_np = np.array(all_z_data)
    else:
        all_z_np = np.array([])

    result["stats"]["total_points"] = len(all_x_np)
    result["stats"]["valid_points"] = len(all_x_np)
    result["stats"]["x_unique_values"] = len(np.unique(all_x_np))
    result["stats"]["y_unique_values"] = len(np.unique(all_y_np))
    if class_col:
        result["stats"]["class_unique_values"] = len(np.unique(all_class_np))
    if z_col:
        result["stats"]["z_unique_values"] = len(np.unique(all_z_np))

    if len(result["stats"]["wells_with_data"]) == 0:
        result["valid"] = False
        result["issues"].append("No wells contain valid data for the selection")
    if result["stats"]["x_unique_values"] < MIN_UNIQUE_VALUES or result["stats"]["y_unique_values"] < MIN_UNIQUE_VALUES:
        result["valid"] = False
        result["issues"].append("Insufficient variation in data")

    return result


def get_robust_limits(data_array: np.ndarray, padding_percent: float = 5) -> Tuple[Optional[float], Optional[float]]:
    """Return axis limits based on *data_array* with padding."""
    if len(data_array) == 0:
        return None, None
    if len(np.unique(data_array)) == 1:
        single_value = data_array[0]
        delta = max(abs(single_value) * 0.01, 1.0)
        return single_value - delta, single_value + delta
    data_min, data_max = np.min(data_array), np.max(data_array)
    data_range = data_max - data_min
    padding = data_range * (padding_percent / 100)
    min_padding = max(abs(data_min) * 0.01, abs(data_max) * 0.01, 0.1)
    padding = max(padding, min_padding)
    return data_min - padding, data_max + padding


def execute_custom_calculations(
    las_file: lasio.LASFile,
    calculations_string: str,
    global_namespace: Optional[Dict[str, Any]] = None,
) -> List[str]:
    """Execute *calculations_string* on *las_file*, adding new curves."""
    ns = {"np": np}
    if global_namespace:
        ns.update(global_namespace)

    local_ns: Dict[str, Any] = {curve: np.array(las_file[curve].data) for curve in las_file.curves.keys()}
    local_ns.update(ns)

    exec(calculations_string, {}, local_ns)

    new_curves: List[str] = []
    for var_name, data in local_ns.items():
        if var_name not in las_file.curves.keys() and var_name != "np":
            if isinstance(data, np.ndarray) and data.shape[0] == len(las_file["DEPTH"].data):
                las_file.append_curve(var_name, data)
                new_curves.append(var_name)
    return new_curves


def execute_calculations_with_validation(las_files: List[lasio.LASFile], calculations: str) -> bool:
    """
    Execute calculations on all LAS files with comprehensive validation and error handling.
    
    Args:
        las_files: List of lasio.LASFile objects
        calculations: String containing the calculation code
        
    Returns:
        bool: True if calculations were successful, False if cancelled or failed
    """
    from tkinter import messagebox
    
    if not calculations or not calculations.strip():
        print("ℹ️ Calculator: No calculations entered. Proceeding without custom calculations.")
        return True  # Successfully "completed" by doing nothing

    # Validate inputs before execution
    print("🔄 Calculator: Validating calculation inputs...")
    validation_result = validate_calculation_inputs(las_files, calculations)

    if not validation_result['valid']:
        print("🔄 Calculator: Validation failed, showing error dialog...")
        # Show detailed error information
        retry = messagebox.askretrycancel(
            "Log Availability Error",
            validation_result['error_details'] + "\n\nWould you like to:\n\n"
            "• Retry: Modify your calculations to use only ✅ logs\n"
            "• Cancel: Skip calculator and continue with existing logs"
        )
        if retry:
            print("🔄 Calculator: User chose retry, but this function cannot retry...")
            return False  # Caller should handle retry
        else:
            print("🔄 Calculator: User chose cancel, continuing workflow...")
            return True  # Skip calculator, continue workflow successfully

    # Execute calculations safely
    print("🔄 Calculator: Executing calculations...")
    error_occurred = False
    all_new_curves = set()  # Track all new curves created across wells
    well_curves_added = {}  # Track which curves were added to each well

    for las in las_files:
        well_name = las.well.WELL.value
        well_curves_added[well_name] = []

        # Prepare the execution environment
        local_ns = {}
        for curve in las.curves.keys():
            local_ns[curve] = np.array(las[curve].data)
        # Make numpy available
        local_ns['np'] = np

        try:
            # Execute the calculations
            exec(calculations, {}, local_ns)
        except Exception as e:
            # Enhanced error message with reference to legend
            error_message = f"CALCULATION EXECUTION ERROR\n\n"
            error_message += f"❌ Error in well: {well_name}\n"
            error_message += f"Error details: {str(e)}\n\n"
            error_message += "🔍 COMMON CAUSES:\n"
            error_message += "• Using logs that don't exist in this well\n"
            error_message += "• Referencing logs marked with ⚠️ in the calculator legend\n"
            error_message += "• Syntax errors in calculation expressions\n"
            error_message += "• Division by zero or invalid mathematical operations\n\n"

            error_message += "\n🔧 HOW TO FIX:\n"
            error_message += "1. Use only logs marked with ✅ (available in all wells)\n"
            error_message += "2. Check the calculator legend before using any logs\n"
            error_message += "3. Use 'Check Log Availability' button to validate before submitting\n"
            error_message += "4. Remove or replace references to ⚠️ logs\n\n"
            error_message += "💡 TIP: The calculator legend shows which logs are safe to use!"

            messagebox.showerror("Calculator Execution Error", error_message)
            error_occurred = True
            break  # Break out of the for loop

        # Add new variables as curves to the LAS file
        new_curves_added = []
        for var_name, data in local_ns.items():
            if var_name not in las.curves.keys() and var_name != 'np':
                # Check if data is array-like and has the correct length
                if isinstance(data, np.ndarray) and data.shape[0] == len(las['DEPTH'].data):
                    # Add the new curve
                    las.append_curve(var_name, data)
                    new_curves_added.append(var_name)
                    all_new_curves.add(var_name)
                    well_curves_added[well_name].append(var_name)
                    print(f"✅ Added calculated curve '{var_name}' to well {well_name}")
                else:
                    # Skip variables that are scalars or arrays of incorrect length
                    print(f"⚠️ Skipped variable '{var_name}' in well {well_name}: incorrect shape or type")
                    continue

        if new_curves_added:
            print(f"📊 Well {well_name}: Added {len(new_curves_added)} new calculated curves: {new_curves_added}")
        else:
            print(f"ℹ️ Well {well_name}: No new curves were added from calculations")

        if error_occurred:
            break

    # Check for consistency across wells
    if not error_occurred and all_new_curves:
        print(f"\n🔍 Calculator: Checking curve consistency across {len(las_files)} wells...")
        print(f"📊 Total new curves created: {sorted(all_new_curves)}")

        # Check which curves are missing from which wells
        missing_curves_report = {}
        for curve_name in all_new_curves:
            wells_missing = []
            for las in las_files:
                well_name = las.well.WELL.value
                if curve_name not in well_curves_added[well_name]:
                    wells_missing.append(well_name)

            if wells_missing:
                missing_curves_report[curve_name] = wells_missing
                print(f"⚠️ Curve '{curve_name}' missing from wells: {wells_missing}")
            else:
                print(f"✅ Curve '{curve_name}' successfully added to all wells")

        # If some curves are missing from some wells, show a warning
        if missing_curves_report:
            warning_message = "⚠️ CURVE CONSISTENCY WARNING\n\n"
            warning_message += "Some calculated curves were not added to all wells:\n\n"
            for curve_name, missing_wells in missing_curves_report.items():
                warning_message += f"• '{curve_name}' missing from: {', '.join(missing_wells)}\n"
            warning_message += "\n💡 These curves will not appear in the column selection dropdown\n"
            warning_message += "because only curves present in ALL wells are shown.\n\n"
            warning_message += "This usually happens when:\n"
            warning_message += "• Calculations produce different variable names in different wells\n"
            warning_message += "• Some wells have data issues that prevent calculation\n"
            warning_message += "• Variable assignments are conditional\n\n"
            warning_message += "✅ Curves available in ALL wells will still be usable."

            messagebox.showwarning("Curve Consistency Warning", warning_message)
        else:
            print("✅ All calculated curves are available in all wells!")

    if not error_occurred:
        # Calculations successful for all LAS files
        print("✅ Calculator: All calculations executed successfully!")

        # Show success message with details about new curves
        success_message = f"✅ Calculations completed successfully!\n\n"
        success_message += f"Processed {len(las_files)} wells\n"

        if all_new_curves:
            success_message += f"Added {len(all_new_curves)} new calculated curves:\n"
            for curve_name in sorted(all_new_curves):
                success_message += f"• {curve_name}\n"
            success_message += "\n✅ All new curves are available in all wells and will appear in column selection."
        else:
            success_message += "No new curves were created (calculations may have been empty or only modified existing data)."

        messagebox.showinfo("Calculator Success", success_message)
        return True
    else:
        # Ask the user if they want to retry or cancel
        retry = messagebox.askretrycancel(
            "Calculation Execution Error",
            "Calculation execution failed. Would you like to:\n\n"
            "• Retry: Modify your calculations and try again\n"
            "• Cancel: Skip calculator and continue with existing logs"
        )
        if not retry:  # User chose Cancel
            print("🔄 Calculator: User chose to skip after execution failure, continuing workflow")
            return True  # Continue workflow without these calculations
        else:  # User chose Retry
            print("🔄 Calculator: User chose to retry after execution failure")
            return False  # Caller should handle retry

